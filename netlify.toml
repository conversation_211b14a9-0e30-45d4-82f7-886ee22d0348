[build]
  publish = "."
  command = "echo 'No build needed for static site'"

[functions]
  directory = "netlify/functions"

# Redirect /api/lead to Netlify function for backward compatibility
[[redirects]]
  from = "/api/lead"
  to = "/.netlify/functions/lead"
  status = 200

# Environment-specific settings
[context.production]
  environment = { NODE_ENV = "production" }

[context.deploy-preview]
  environment = { NODE_ENV = "staging" }

[context.branch-deploy]
  environment = { NODE_ENV = "staging" }
