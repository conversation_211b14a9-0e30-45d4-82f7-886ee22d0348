// netlify/functions/lead.js
exports.handler = async function(event, context) {
  if (event.httpMethod !== 'POST') {
    return { statusCode: 405, body: 'Method Not Allowed' };
  }

  try {
    const payload = JSON.parse(event.body || '{}');

    // anti-spam simple (honeypot & timing)
    const honeypot = payload.hp || ''; // champ invisible dans le form
    const ts = payload.ts ? Number(payload.ts) : 0;
    const now = Date.now();

    if (honeypot && honeypot.trim() !== '') {
      return { statusCode: 400, body: JSON.stringify({ error: 'Spam detected' }) };
    }
    if (ts && (now - ts) < 2000) { // moins de 2s -> probablement bot
      return { statusCode: 400, body: JSON.stringify({ error: 'Spam timing' }) };
    }

    // sanitize/whitelist fields
    const fields = {
      "Nom complet": payload.name || '',
      "Entreprise": payload.company || '',
      "Email": payload.email || '',
      "Téléphone": payload.phone || '',
      "Statut": payload.status || '',
      "Contexte": payload.context || '',
      "UTM Source": payload.utm_source || '',
      "UTM Campaign": payload.utm_campaign || '',
      "Date": new Date().toISOString()
    };

    // Basic validation
    if (!fields["Nom complet"] || !fields["Email"] || !fields["Entreprise"]) {
      return { statusCode: 400, body: JSON.stringify({ error: 'Missing required fields' }) };
    }

    const AIRTABLE_KEY = process.env.AIRTABLE_API_KEY;
    const AIRTABLE_BASE = process.env.AIRTABLE_BASE_ID;

    if (!AIRTABLE_KEY || !AIRTABLE_BASE) {
      console.error('Missing Airtable env vars');
      return { statusCode: 500, body: JSON.stringify({ error: 'Server config error' }) };
    }

    const res = await fetch(`https://api.airtable.com/v0/${AIRTABLE_BASE}/Leads`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AIRTABLE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ fields })
    });

    const json = await res.json();

    if (!res.ok) {
      console.error('Airtable error', json);
      return { statusCode: res.status || 500, body: JSON.stringify({ error: 'Airtable error', details: json }) };
    }

    return { statusCode: 200, body: JSON.stringify({ success: true, id: json.id }) };
  } catch (err) {
    console.error(err);
    return { statusCode: 500, body: JSON.stringify({ error: 'Server error' }) };
  }
};
