// netlify/functions/lead.js - Modern Netlify Functions API
export default async function(req, context) {
  console.log('🚀 Netlify function called:', {
    method: req.method,
    url: req.url,
    headers: Object.fromEntries(req.headers.entries())
  });

  if (req.method !== 'POST') {
    return new Response('Method Not Allowed', { status: 405 });
  }

  try {
    const payload = await req.json();
    console.log('📦 Received payload:', payload);

    // anti-spam simple (honeypot & timing)
    const honeypot = payload.hp || ''; // champ invisible dans le form
    const ts = payload.ts ? Number(payload.ts) : 0;
    const now = Date.now();

    if (honeypot && honeypot.trim() !== '') {
      return new Response(JSON.stringify({ error: 'Spam detected' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    if (ts && (now - ts) < 2000) { // moins de 2s -> probablement bot
      return new Response(JSON.stringify({ error: 'Spam timing' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // sanitize/whitelist fields - aligned with local API
    const fields = {
      "Nom": payload.name || '',
      "Entreprise": payload.company || '',
      "Email": payload.email || '',
      "Téléphone": payload.phone || '',
      "Statut": payload.status || '',
      "Contexte": payload.context || '',
      "utm_source": payload.utm_source || '',
      "utm_campaign": payload.utm_campaign || '',
      "Date": new Date().toISOString()
    };

    // Basic validation
    if (!fields["Nom complet"] || !fields["Email"] || !fields["Entreprise"]) {
      return new Response(JSON.stringify({ error: 'Missing required fields' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Support both naming conventions for environment variables
    const AIRTABLE_KEY = Netlify.env.get('AIRTABLE_KEY') || Netlify.env.get('AIRTABLE_API_KEY');
    const AIRTABLE_BASE = Netlify.env.get('AIRTABLE_BASE') || Netlify.env.get('AIRTABLE_BASE_ID');

    if (!AIRTABLE_KEY || !AIRTABLE_BASE) {
      console.error('Missing Airtable env vars');
      return new Response(JSON.stringify({ error: 'Server config error' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const res = await fetch(`https://api.airtable.com/v0/${AIRTABLE_BASE}/Prospects`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AIRTABLE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ fields })
    });

    const json = await res.json();

    if (!res.ok) {
      console.error('Airtable error', json);
      return new Response(JSON.stringify({ error: 'Airtable error', details: json }), {
        status: res.status || 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({ success: true, id: json.id }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (err) {
    console.error(err);
    return new Response(JSON.stringify({ error: 'Server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
