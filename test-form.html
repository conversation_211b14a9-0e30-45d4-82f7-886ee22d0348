<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Formulaire de Contact</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        input, select, textarea { width: 100%; padding: 8px; margin: 5px 0; box-sizing: border-box; }
    </style>
</head>
<body>
    <h1>Test du Formulaire de Contact HAI Systems</h1>
    
    <div class="test-section info">
        <h2>🔍 Détection d'Environnement</h2>
        <p><strong>Hostname actuel:</strong> <span id="hostname"></span></p>
        <p><strong>Endpoint API détecté:</strong> <span id="api-endpoint"></span></p>
        <p><strong>Type d'environnement:</strong> <span id="env-type"></span></p>
    </div>

    <div class="test-section">
        <h2>📝 Test du Formulaire</h2>
        <form id="test-form">
            <input type="text" name="name" placeholder="Nom complet" value="Test User" required>
            <input type="text" name="company" placeholder="Entreprise" value="Test Company" required>
            <input type="email" name="email" placeholder="Email pro" value="<EMAIL>" required>
            <input type="text" name="phone" placeholder="Téléphone" value="+33123456789">
            <select name="status">
                <option value="reprise">Reprise récente</option>
                <option value="optimisation">Optimisation</option>
                <option value="autre">Autre</option>
            </select>
            <textarea name="context" placeholder="Contexte" rows="3">Test de soumission du formulaire</textarea>
            <button type="submit">🚀 Tester la Soumission</button>
        </form>
    </div>

    <div class="test-section" id="results" style="display: none;">
        <h2>📊 Résultats du Test</h2>
        <div id="test-output"></div>
    </div>

    <script>
        // Fonction de détection d'environnement (copiée depuis index.html)
        function getApiEndpoint() {
            const hostname = window.location.hostname;
            const isNetlify = hostname.includes('netlify.app') || 
                             hostname.includes('staging.hai-systems.com') ||
                             hostname.includes('hai-systems.com');
            
            if (isNetlify) {
                return '/.netlify/functions/lead';
            }
            return '/api/lead';
        }

        // Affichage des informations d'environnement
        document.getElementById('hostname').textContent = window.location.hostname;
        document.getElementById('api-endpoint').textContent = getApiEndpoint();
        
        const hostname = window.location.hostname;
        let envType = 'Local Development';
        if (hostname.includes('staging')) envType = 'Staging';
        else if (hostname.includes('hai-systems.com')) envType = 'Production';
        else if (hostname.includes('netlify.app')) envType = 'Netlify Preview';
        document.getElementById('env-type').textContent = envType;

        // Test du formulaire
        document.getElementById('test-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const resultsDiv = document.getElementById('results');
            const outputDiv = document.getElementById('test-output');
            resultsDiv.style.display = 'block';
            outputDiv.innerHTML = '<p>⏳ Test en cours...</p>';

            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            const apiEndpoint = getApiEndpoint();

            try {
                console.log('🔗 Envoi vers:', apiEndpoint);
                console.log('📦 Données:', data);

                const response = await fetch(apiEndpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                const responseText = await response.text();
                let responseData = null;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = { raw: responseText };
                }

                if (response.ok) {
                    outputDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Succès!</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Réponse:</strong> <pre>${JSON.stringify(responseData, null, 2)}</pre></p>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Erreur</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Réponse:</strong> <pre>${JSON.stringify(responseData, null, 2)}</pre></p>
                        </div>
                    `;
                }
            } catch (error) {
                outputDiv.innerHTML = `
                    <div class="error">
                        <h3>🚨 Erreur Réseau</h3>
                        <p><strong>Message:</strong> ${error.message}</p>
                        <p><strong>Endpoint testé:</strong> ${apiEndpoint}</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
