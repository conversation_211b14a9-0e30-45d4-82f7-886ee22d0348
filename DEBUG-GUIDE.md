# 🔧 Guide de Debug - Formulaire de Contact

## 🚨 Problème Actuel

Le site staging (`staging.hai-systems.com`) utilise encore l'ancien code et appelle `/api/lead` au lieu d'utiliser la nouvelle logique de détection d'environnement.

## ✅ Solutions Appliquées

### 1. Détection d'Environnement Corrigée

```javascript
function getApiEndpoint() {
    const hostname = window.location.hostname;
    const protocol = window.location.protocol;
    const isFileProtocol = protocol === 'file:';
    const isLocalhost = hostname.includes('localhost') || hostname.includes('127.0.0.1') || hostname === '';
    const isLocal = isLocalhost && !isFileProtocol;
    
    if (isFileProtocol) {
        return '/api/lead'; // Pour file://
    }
    
    if (isLocal) {
        return '/api/lead'; // Pour localhost
    }
    
    return '/.netlify/functions/lead'; // Pour staging/production
}
```

### 2. Fonction Netlify Migrée

- ✅ Ancienne API: `exports.handler` → Nouvelle API: `export default`
- ✅ Extension: `.js` → `.mjs` (ES modules)
- ✅ Variables d'env: `process.env` → `Netlify.env.get()`

## 🚀 Étapes de Déploiement

### 1. Vérifier les Changements Locaux

```bash
# Démarrer le serveur local pour test
node serve-local.js

# Tester sur http://localhost:3000
# Le formulaire devrait utiliser /api/lead (mock)
```

### 2. Déployer sur Staging

```bash
# Pousser les changements
git add .
git commit -m "Fix: Migrate to new Netlify Functions API and fix environment detection"
git push origin main
```

### 3. Vérifier le Déploiement

1. **Attendre le déploiement** (2-5 minutes)
2. **Vider le cache** du navigateur (Ctrl+F5)
3. **Tester sur** `staging.hai-systems.com`

## 🔍 Tests à Effectuer

### Test 1: Détection d'Environnement

Ouvrir la console sur `staging.hai-systems.com` et vérifier:

```
🔧 === ENVIRONMENT DEBUG INFO ===
Current URL: https://staging.hai-systems.com/
Hostname: staging.hai-systems.com
Detected API endpoint: /.netlify/functions/lead
=================================
```

### Test 2: Soumission du Formulaire

Remplir et soumettre le formulaire. Console attendue:

```
🔍 Environment Detection Debug:
  - Hostname: staging.hai-systems.com
  - Protocol: https:
  - Is File Protocol: false
  - Is Local Dev: false
  - Remote environment detected, using Netlify function endpoint

🎯 Primary endpoint: /.netlify/functions/lead
✅ Primary endpoint succeeded
```

### Test 3: Fonction Netlify

Dans les logs Netlify, vous devriez voir:

```
🚀 Netlify function called: { method: 'POST', url: '...' }
📦 Received payload: { name: '...', company: '...' }
🔑 Environment variables check: { hasAirtableKey: true, ... }
✅ Success! Created record: rec123456789
```

## 🛠️ Outils de Debug

### 1. Page de Test Locale

```bash
node serve-local.js
# Puis aller sur http://localhost:3000/test-form.html
```

### 2. Page de Test Staging

Aller sur `staging.hai-systems.com/test-netlify-function.html`

### 3. Console du Navigateur

Vérifier les logs de détection d'environnement et d'appels API.

## ❌ Problèmes Possibles

### 1. Cache du Navigateur

**Symptôme**: Toujours `/api/lead` au lieu de `/.netlify/functions/lead`

**Solution**: 
- Ctrl+F5 (hard refresh)
- Ouvrir en navigation privée
- Vider le cache manuellement

### 2. Déploiement Non Terminé

**Symptôme**: Pas de changement visible

**Solution**:
- Vérifier le statut de déploiement dans Netlify
- Attendre la fin du déploiement
- Vérifier les logs de build

### 3. Variables d'Environnement

**Symptôme**: "Server config error"

**Solution**:
- Vérifier que `AIRTABLE_KEY` et `AIRTABLE_BASE` sont configurées
- Dans Netlify Dashboard → Site Settings → Environment Variables

### 4. Fonction Non Déployée

**Symptôme**: 404 sur `/.netlify/functions/lead`

**Solution**:
- Vérifier que `netlify/functions/lead.mjs` existe
- Vérifier le `netlify.toml`
- Redéployer manuellement si nécessaire

## 🎯 Résultat Attendu

Après déploiement et test:

1. ✅ Console montre `/.netlify/functions/lead` sur staging
2. ✅ Formulaire se soumet sans erreur 404
3. ✅ Données arrivent dans Airtable
4. ✅ Message de succès s'affiche

## 📞 Si Ça Ne Marche Toujours Pas

1. **Vérifier les logs Netlify** pour voir si la fonction est appelée
2. **Tester directement** `https://staging.hai-systems.com/.netlify/functions/lead` avec Postman
3. **Vérifier les variables d'environnement** dans Netlify Dashboard
4. **Redéployer manuellement** depuis Netlify Dashboard
